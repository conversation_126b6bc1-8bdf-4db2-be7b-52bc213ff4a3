#!/usr/bin/env python3
# feedback_processor.py

"""
代码评审反馈处理脚本
用于收集、处理和存储用户对AI代码评审的反馈
"""

import os
import re
import sys
import json
import time
import datetime
import uuid
import argparse
from pathlib import Path

# 配置常量
FEEDBACK_DIR = Path("./feedback-database")
CORRECT_FEEDBACK_FILE = FEEDBACK_DIR / "correct-reviews.md"
INCORRECT_FEEDBACK_FILE = FEEDBACK_DIR / "incorrect-reviews.md"

# 确保反馈目录存在
FEEDBACK_DIR.mkdir(exist_ok=True)

def generate_feedback_id():
    """生成唯一的反馈ID"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    unique_id = str(uuid.uuid4())[:6]
    return f"{timestamp}-{unique_id}"

def categorize_feedback(content):
    """根据反馈内容确定评审类别"""
    categories = {
        "Redux状态管理": ["redux", "state", "action", "reducer", "middleware", "store", "dispatch"],
        "Flutter UI": ["widget", "flutter", "ui", "build", "layout", "render"],
        "性能优化": ["性能", "优化", "performance", "optimize", "efficient", "速度", "memory"],
        "代码复杂度": ["复杂", "判断", "嵌套", "refactor", "简化"],
        "类型安全": ["类型", "type", "dynamic", "null safety", "泛型", "generic"],
        "代码风格": ["风格", "style", "format", "命名", "name", "convention"]
    }
    
    content_lower = content.lower()
    for category, keywords in categories.items():
        for keyword in keywords:
            if keyword.lower() in content_lower:
                return category
    
    return "其他"

def process_correct_feedback(review_point, feedback_content):
    """处理正确评审反馈"""
    # 提取相关代码上下文（如果存在）
    code_context = ""
    code_match = re.search(r'```[\s\S]*?```', review_point, re.DOTALL)
    if code_match:
        code_context = code_match.group(0)
    
    # 确定评审类别
    category = categorize_feedback(review_point + " " + feedback_content)
    
    # 生成反馈ID
    feedback_id = generate_feedback_id()
    
    # 创建反馈条目
    feedback_entry = f"""
### 反馈ID: {feedback_id}
- **评审点**: {review_point.strip()}
- **代码上下文**: {code_context}
- **评审意见**: {review_point.strip()}
- **反馈内容**: {feedback_content.strip()}
- **反馈时间**: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **标签**: {category}
"""
    
    # 添加到正确评审文件
    add_to_feedback_file(CORRECT_FEEDBACK_FILE, category, feedback_entry)
    return feedback_id

def process_incorrect_feedback(review_point, error_reason, correct_opinion):
    """处理不正确评审反馈"""
    # 提取相关代码上下文（如果存在）
    code_context = ""
    code_match = re.search(r'```[\s\S]*?```', review_point, re.DOTALL)
    if code_match:
        code_context = code_match.group(0)
    
    # 确定评审类别
    category = categorize_feedback(review_point + " " + error_reason + " " + correct_opinion)
    
    # 生成反馈ID
    feedback_id = generate_feedback_id()
    
    # 创建反馈条目
    feedback_entry = f"""
### 反馈ID: {feedback_id}
- **评审点**: {review_point.strip()}
- **代码上下文**: {code_context}
- **错误评审**: {review_point.strip()}
- **错误原因**: {error_reason.strip()}
- **正确评审**: {correct_opinion.strip()}
- **反馈时间**: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **标签**: {category}
"""
    
    # 添加到不正确评审文件
    add_to_feedback_file(INCORRECT_FEEDBACK_FILE, category, feedback_entry)
    return feedback_id

def add_to_feedback_file(file_path, category, feedback_entry):
    """将反馈添加到指定文件中"""
    # 确保文件存在
    if not file_path.exists():
        file_path.write_text("# " + file_path.name.split('.')[0].replace('-', ' ').title() + "\n\n")
    
    content = file_path.read_text()
    
    # 检查类别是否已存在
    category_header = f"## 评审类别：{category}"
    if category_header in content:
        # 在类别下添加反馈
        pattern = re.escape(category_header) + r"[\s\S]*?(?=## |$)"
        match = re.search(pattern, content)
        if match:
            category_content = match.group(0)
            updated_category = category_content + feedback_entry
            content = content.replace(category_content, updated_category)
    else:
        # 添加新类别和反馈
        content += f"\n{category_header}\n{feedback_entry}\n"
    
    # 写回文件
    file_path.write_text(content)

def parse_feedback_command(command):
    """解析反馈命令"""
    parts = command.split(' ', 1)
    if len(parts) < 2:
        return None, None
    
    cmd_type = parts[0].lower()
    args = parts[1]
    
    if cmd_type not in ['correct', 'incorrect']:
        return None, None
    
    try:
        # 尝试解析引号内的参数
        pattern = r'"([^"]*)"'
        arg_matches = re.findall(pattern, args)
        
        # 如果没有使用引号，尝试使用空格分割
        if not arg_matches:
            arg_parts = args.split()
            if cmd_type == 'correct':
                if len(arg_parts) >= 2:
                    return cmd_type, {'review_point': arg_parts[0], 'feedback_content': ' '.join(arg_parts[1:])}
            else:  # incorrect
                if len(arg_parts) >= 3:
                    return cmd_type, {'review_point': arg_parts[0], 'error_reason': arg_parts[1], 'correct_opinion': ' '.join(arg_parts[2:])}
        else:
            if cmd_type == 'correct' and len(arg_matches) >= 2:
                return cmd_type, {'review_point': arg_matches[0], 'feedback_content': arg_matches[1]}
            elif cmd_type == 'incorrect' and len(arg_matches) >= 3:
                return cmd_type, {'review_point': arg_matches[0], 'error_reason': arg_matches[1], 'correct_opinion': arg_matches[2]}
    except Exception as e:
        print(f"解析命令时出错: {e}")
    
    return None, None

def process_batch_feedback(commands):
    """处理批量反馈命令"""
    results = {
        'correct': [],
        'incorrect': [],
        'failed': []
    }
    
    for cmd in commands:
        cmd = cmd.strip()
        if not cmd or cmd.startswith('#'):
            continue  # 跳过空行和注释
        
        cmd_type, args = parse_feedback_command(cmd)
        
        if cmd_type == 'correct':
            try:
                feedback_id = process_correct_feedback(args['review_point'], args['feedback_content'])
                results['correct'].append({'command': cmd, 'id': feedback_id})
            except Exception as e:
                results['failed'].append({'command': cmd, 'error': str(e)})
        
        elif cmd_type == 'incorrect':
            try:
                feedback_id = process_incorrect_feedback(args['review_point'], args['error_reason'], args['correct_opinion'])
                results['incorrect'].append({'command': cmd, 'id': feedback_id})
            except Exception as e:
                results['failed'].append({'command': cmd, 'error': str(e)})
        
        else:
            results['failed'].append({'command': cmd, 'error': '无法识别的命令格式'})
    
    return results

def display_batch_results(results):
    """显示批量处理结果"""
    print("\n批量反馈处理结果:")
    print(f"成功处理: {len(results['correct']) + len(results['incorrect'])} 条")
    print(f"  - 正确评审: {len(results['correct'])} 条")
    print(f"  - 不正确评审: {len(results['incorrect'])} 条")
    print(f"处理失败: {len(results['failed'])} 条")
    
    if results['correct']:
        print("\n成功添加的正确评审:")
        for item in results['correct']:
            print(f"  - ID: {item['id']}, 命令: {item['command'][:50]}...")
    
    if results['incorrect']:
        print("\n成功添加的不正确评审:")
        for item in results['incorrect']:
            print(f"  - ID: {item['id']}, 命令: {item['command'][:50]}...")
    
    if results['failed']:
        print("\n处理失败的命令:")
        for item in results['failed']:
            print(f"  - 错误: {item['error']}")
            print(f"    命令: {item['command']}")

def show_usage():
    """显示使用说明"""
    print("""
代码评审反馈处理工具

使用方法:
    python feedback_processor.py correct "评审点" "评价内容"
    python feedback_processor.py incorrect "评审点" "错误原因" "正确的评审意见"
    python feedback_processor.py batch [--file FILE | --commands CMD1 CMD2 ...]
    python feedback_processor.py stats
    python feedback_processor.py search "关键词"
    
示例:
    python feedback_processor.py correct "问题1-条件判断复杂化" "评审意见准确"
    python feedback_processor.py incorrect "问题3-状态管理" "误解了Redux流程" "应该使用不可变更新"
    python feedback_processor.py batch --commands 'correct "点1" "反馈1"' 'incorrect "点2" "原因2" "意见2"'
    python feedback_processor.py batch --file feedback_batch.txt
    python feedback_processor.py stats
    python feedback_processor.py search "Redux"
    
批量命令文件格式 (每行一条命令):
    correct "评审点1" "评价内容1"
    incorrect "评审点2" "错误原因2" "正确的评审意见2"
    # 这是注释行，将被忽略
    correct "评审点3" "评价内容3"
    """)

def show_stats():
    """显示反馈统计信息"""
    stats = {"correct": 0, "incorrect": 0, "categories": {}}
    
    # 统计正确评审
    if CORRECT_FEEDBACK_FILE.exists():
        content = CORRECT_FEEDBACK_FILE.read_text()
        feedback_count = len(re.findall(r'### 反馈ID:', content))
        stats["correct"] = feedback_count
        
        # 类别统计
        for category_match in re.finditer(r'## 评审类别：(.*?)(?=\n)', content):
            category = category_match.group(1)
            if category not in stats["categories"]:
                stats["categories"][category] = {"correct": 0, "incorrect": 0}
            
            # 计算该类别下的反馈数量
            category_pattern = re.escape(f"## 评审类别：{category}") + r"[\s\S]*?(?=## |$)"
            category_content = re.search(category_pattern, content)
            if category_content:
                count = len(re.findall(r'### 反馈ID:', category_content.group(0)))
                stats["categories"][category]["correct"] = count
    
    # 统计不正确评审
    if INCORRECT_FEEDBACK_FILE.exists():
        content = INCORRECT_FEEDBACK_FILE.read_text()
        feedback_count = len(re.findall(r'### 反馈ID:', content))
        stats["incorrect"] = feedback_count
        
        # 类别统计
        for category_match in re.finditer(r'## 评审类别：(.*?)(?=\n)', content):
            category = category_match.group(1)
            if category not in stats["categories"]:
                stats["categories"][category] = {"correct": 0, "incorrect": 0}
            
            # 计算该类别下的反馈数量
            category_pattern = re.escape(f"## 评审类别：{category}") + r"[\s\S]*?(?=## |$)"
            category_content = re.search(category_pattern, content)
            if category_content:
                count = len(re.findall(r'### 反馈ID:', category_content.group(0)))
                stats["categories"][category]["incorrect"] = count
    
    # 显示统计结果
    print("\n代码评审反馈统计:")
    print(f"正确评审: {stats['correct']}")
    print(f"不正确评审: {stats['incorrect']}")
    print(f"总计: {stats['correct'] + stats['incorrect']}")
    
    print("\n按类别统计:")
    for category, counts in stats["categories"].items():
        total = counts["correct"] + counts["incorrect"]
        if total > 0:
            correct_percent = counts["correct"] / total * 100
            print(f"- {category}: 总计 {total} (正确 {counts['correct']}, 不正确 {counts['incorrect']}, 正确率 {correct_percent:.1f}%)")
    
    return stats

def search_feedback(keyword):
    """搜索反馈库"""
    results = {"correct": [], "incorrect": []}
    keyword = keyword.lower()
    
    # 搜索正确评审
    if CORRECT_FEEDBACK_FILE.exists():
        content = CORRECT_FEEDBACK_FILE.read_text()
        for feedback_match in re.finditer(r'### 反馈ID:[\s\S]*?(?=### 反馈ID:|$)', content, re.DOTALL):
            feedback = feedback_match.group(0)
            if keyword in feedback.lower():
                results["correct"].append(feedback.strip())
    
    # 搜索不正确评审
    if INCORRECT_FEEDBACK_FILE.exists():
        content = INCORRECT_FEEDBACK_FILE.read_text()
        for feedback_match in re.finditer(r'### 反馈ID:[\s\S]*?(?=### 反馈ID:|$)', content, re.DOTALL):
            feedback = feedback_match.group(0)
            if keyword in feedback.lower():
                results["incorrect"].append(feedback.strip())
    
    # 显示结果
    print(f"\n关键词 '{keyword}' 的搜索结果:")
    print(f"正确评审: {len(results['correct'])}")
    print(f"不正确评审: {len(results['incorrect'])}")
    
    if results["correct"]:
        print("\n正确评审结果:")
        for idx, feedback in enumerate(results["correct"], 1):
            print(f"\n--- 结果 {idx} ---\n{feedback}")
    
    if results["incorrect"]:
        print("\n不正确评审结果:")
        for idx, feedback in enumerate(results["incorrect"], 1):
            print(f"\n--- 结果 {idx} ---\n{feedback}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="代码评审反馈处理工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # correct 命令
    correct_parser = subparsers.add_parser('correct', help='添加正确评审反馈')
    correct_parser.add_argument('review_point', help='评审点')
    correct_parser.add_argument('feedback_content', help='评价内容')
    
    # incorrect 命令
    incorrect_parser = subparsers.add_parser('incorrect', help='添加不正确评审反馈')
    incorrect_parser.add_argument('review_point', help='评审点')
    incorrect_parser.add_argument('error_reason', help='错误原因')
    incorrect_parser.add_argument('correct_opinion', help='正确的评审意见')
    
    # batch 命令
    batch_parser = subparsers.add_parser('batch', help='批量处理多条反馈')
    batch_group = batch_parser.add_mutually_exclusive_group(required=True)
    batch_group.add_argument('--file', '-f', help='包含多条反馈命令的文件路径')
    batch_group.add_argument('--commands', '-c', nargs='+', help='反馈命令列表')
    
    # stats 命令
    subparsers.add_parser('stats', help='显示反馈统计信息')
    
    # search 命令
    search_parser = subparsers.add_parser('search', help='搜索反馈库')
    search_parser.add_argument('keyword', help='搜索关键词')
    
    # 如果没有参数，使用旧的参数解析方式以保持向后兼容性
    if len(sys.argv) == 1:
        show_usage()
        return
    
    # 检查是否使用旧式命令格式
    if sys.argv[1] in ['correct', 'incorrect', 'stats', 'search'] and not '--' in ' '.join(sys.argv):
        # 使用旧式命令格式
        command = sys.argv[1].lower()
        
        if command == "correct" and len(sys.argv) >= 4:
            review_point = sys.argv[2]
            feedback_content = sys.argv[3]
            feedback_id = process_correct_feedback(review_point, feedback_content)
            print(f"正确评审反馈已添加，ID: {feedback_id}")
        
        elif command == "incorrect" and len(sys.argv) >= 5:
            review_point = sys.argv[2]
            error_reason = sys.argv[3]
            correct_opinion = sys.argv[4]
            feedback_id = process_incorrect_feedback(review_point, error_reason, correct_opinion)
            print(f"不正确评审反馈已添加，ID: {feedback_id}")
        
        elif command == "stats":
            show_stats()
        
        elif command == "search" and len(sys.argv) >= 3:
            keyword = sys.argv[2]
            search_feedback(keyword)
        
        elif command == "batch" and len(sys.argv) >= 3:
            # 处理旧式批量命令方式
            if sys.argv[2] == "--file" and len(sys.argv) >= 4:
                file_path = sys.argv[3]
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        commands = f.readlines()
                    results = process_batch_feedback(commands)
                    display_batch_results(results)
                except Exception as e:
                    print(f"处理批量反馈文件时出错: {e}")
            elif sys.argv[2] == "--commands" and len(sys.argv) >= 4:
                commands = sys.argv[3:]
                results = process_batch_feedback(commands)
                display_batch_results(results)
            else:
                show_usage()
        else:
            show_usage()
    else:
        # 使用新的参数解析方式
        try:
            args = parser.parse_args()
            
            if args.command == 'correct':
                feedback_id = process_correct_feedback(args.review_point, args.feedback_content)
                print(f"正确评审反馈已添加，ID: {feedback_id}")
            
            elif args.command == 'incorrect':
                feedback_id = process_incorrect_feedback(args.review_point, args.error_reason, args.correct_opinion)
                print(f"不正确评审反馈已添加，ID: {feedback_id}")
            
            elif args.command == 'batch':
                if args.file:
                    try:
                        with open(args.file, 'r', encoding='utf-8') as f:
                            commands = f.readlines()
                        results = process_batch_feedback(commands)
                        display_batch_results(results)
                    except Exception as e:
                        print(f"处理批量反馈文件时出错: {e}")
                elif args.commands:
                    results = process_batch_feedback(args.commands)
                    display_batch_results(results)
            
            elif args.command == 'stats':
                show_stats()
            
            elif args.command == 'search':
                search_feedback(args.keyword)
            
            else:
                show_usage()
        except Exception as e:
            print(f"错误: {e}")
            show_usage()

if __name__ == "__main__":
    main()
