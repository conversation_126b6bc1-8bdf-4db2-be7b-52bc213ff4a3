交底书内容

1 本发明要解决的技术问题是什么？


*随着大前端概念的提出，为了满足快速开发迭代的行业需求，越来越多的APP采用混合开发模式，业务逻辑不复杂的展示业务，转为H**5**进行开发，原生搭建APP整体框架和业务复杂业务；混合开发模式，可以快速更新业务需求，可以热更新已知问题。*

*具体到物联网方向上，物联设备数据在移动APP中的交互，需要H**5**侧从原生侧获取设备最新数据，H**5**侧的用户操作需要传递给原生侧；在双向的流转过程中，这是一种混合开发，开发过程中暴露出一些问题，业务需求中涉及到原生能力，就需要原生暴露相关能力接口给H**5**调用，实现对应的能力；在H**5**和原生交互过程中，因为原生两端系统的差异，H**5**项目代码里需要针对不同系统做识别，分别对应出不同的代码逻辑，代码维护成本高，可读性差*

2、详细介绍技术背景，并描述已有的与本发明最相近似的技术方案


*在物联网方向上，物联设备重要特性就是可以通过移动端进行远程操控；物联设备接入量大，功能更新频率高，为了满足快速接入和快速迭代需求，目前在移动端物联设备的接入业界都采用混合开发模式，利用H**5**开发速度快，支持热更新的特性，采用H**5**进行物联设备的详情页开发。*

*在H**5**开发物联设备的详情页过程中，对于物联设备的设备状态数据获取和用户操作数据传递流程需要和原生开发进行交互，双方沟通功能接口需求，原生会根据接口需求，开发相应接口在webview容器内暴露给H**5**侧；在实际的操作过程中，因为系统差异，iOS和Android对于同一接口需求，会出现行为上差异，无法完全统一；对于H**5**侧来说，如果接口在两端出现行为差异，这种差异就需要H**5**侧在代码逻辑里判断不同系统，然后针对不同的差异，分别作出不同的处理逻辑。*

*更有超级APP，整个原生框架内集成多种webview容器，因为webview容器内部原理不同，同一个接口需求在不同webview容器下，原生侧对外暴露的接口差异性更大，为了适配不同webview容器，这使得H**5**的开发工作量陡增。*

3、以因果关系推理的方式推导出现有技术的缺点是什么？针对这些缺点，说明本发明的目的。


*现有的实现方式基本都是H**5**里针对Android和IOS端使用不同的逻辑实现相同的产品功能，甚至需要适配不同的webview容器，可表现以下几方面问题：*

1. *一套H**5**代码，同时在iOS和Android两端使用，因某些差异需要针对iOS和Android适配两套调用逻辑*
2. *iOS和Android同一功能，因回调数据差异需要针对iOS和Android适配两套数据处理逻辑*
3. *一套H**5**代码，需要适配APP内多种webview容器，每种webview容器内部逻辑不尽相同，H**5**代码需要针对不同webview容器，单独适配*
4. *代码业务复杂，可读性和可维护性变差*
5. *H**5**逻辑和原生的逻辑耦合性较高*

*本发明通过在H**5**侧和原生侧之间增加一个中间件，做为H**5**和原生的一个沟通桥梁；中间件对于H**5**侧来说，可以保证为H**5**侧在不同的webview容器、不同的系统中使用同一API接口表现的行为是一致的。H**5**侧不需要关心差异，保证H**5**侧代码可维护性和可阅读性保持在一个较好的水平上。*

4、本发明技术方案的详细阐述，应该结合流程图、原理图、电路图、时序图进行说明


上图为H5侧、中间件和原生侧的时序图

   H5侧需要在项目里依赖中间件，中间件随着H5侧项目初始化，暴露出API接口给H5侧；H5侧使用中间件暴露出的API接口可获取物联设备的设备状态数据，用户在H5侧进行设备操作后，可以通过中间件将操作数据传递个原生侧，进而实现设备的控制，并得到控制结果，具体逻辑如下：

1. H5侧项目首先引入中间件，项目在初始化成功后，调用中间件的初始化API接口，中间件的初始化API接口被触发后，会判断当前运行环境的系统和容器情况，根据不同情况，执行不同的初始化逻辑，向原生侧请求不同的初始化资源，原生返回所需资源化，中间件进行初始化操作，中间件初始化成功后，反馈给H5侧初始化结果。
2. H5侧收到中间件初始化成功通知后，可使用中间件暴露出的API接口，此时可使用获取物联设备属性接口，中间件API接口被调用后，根据当前系统和容器情况，对H5侧数据传递进来的数据进行差异化处理，处理完毕后，调用原生侧对应功能的接口；原生侧执行完毕后，将物联设备的相关数据返回给中间件，中间针对原生侧数据进行差异化处理，抹平差异，按照统一格式进行封装，并将封装好的统统一数据返回给H5侧，H5侧收到中间件数据后，处理数据并刷新H5侧显示样式。
3. 用户在H5侧对设备进行交互操作后，H5侧将用户的操作行为转换为数据，使用中间件的API接口下发数据，后续处理逻辑和步骤2相同，区别在于最终返回给H5侧的是操作结果数据。

（1）图2为本发明关于中间件初始化系统容器判断逻辑

5.本发明的关键点和欲保护点是什么？


（1） 中间件对系统和容器的判断，内部针对不同容器进行封装，同一个接口在不同环境下调用方式是一致的

（2） 中间件对原生侧数据返回的数据统一处理，同一接口在不同环境下，返回的数据格式是一致的

6.用推理方式推导出本发明的优点


随着移动端混合开发模式的大发展，各种webview容器百家争鸣，业务需求是千变万化的，APP内会集成多个webview容器满足不同业务需求，各webview容器相同功能也会有差异，这对一套代码多端适用的H5侧来说是个很大的挑战，适配过程中极其复杂，代码臃肿，可维护性和可读性变差。

中间件的模式，可以为H5侧消除APP内不同webview容器的差异性，中间件来负责去抹平这种差异性，对外暴露出一套行为一致的API接口，H5侧引入中间件，并直接使用中间件暴露出的API接口，无需关系H5侧是运行在哪种环境中，这极大的缩短了开发周期，使得H5侧的代码专注于业务需求本身，有利于项目的快速迭代。
