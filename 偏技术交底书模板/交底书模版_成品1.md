交底书内容

1 本发明要解决的技术问题是什么？

对于关键数据、关键业务数据安全性高，为保证数据传输的安全，本文描述通过动态获取秘钥的方式保证H5数据传输的安全性，防止攻击者获取到请求方式后模拟攻击。

2、详细介绍技术背景，并描述已有的与本发明最相近似的技术方案

普通H5数据请求，比较容易破解，通过查看H5页面源码，即可获得数据请求方式；或通过Http抓包截获请求数据，从而可以模拟数据请求进行攻击或获取利益。

本文主要描述通过使用动态加密方式、动态秘钥，对数据进行加密、签名校验，保证H5请求时数据安全机制。存在类似的方案，方案一：使用证书加密，数据传输过程H5使用公钥对数据进行加密解密。方案二：使用盐值（随机码）对数据进行签名校验。

3、以因果关系推理的方式推导出现有技术的缺点是什么？针对这些缺点，说明本发明的目的。


每次数据请求之前都会获取加密算法及秘钥，相对来说会增加Server的负荷及请求的时间。

针对关键及敏感的业务使用本方案能够保证传输的安全性。

4、本发明技术方案的详细阐述，应该结合流程图、原理图、电路图、时序图进行说明


H5页面通过APP发起网络请求而不直接发起，防止请求加密或签名方式的泄漏。在发起真正请求前先申请加密方式及秘钥，秘钥动态生成，因为数据是动态的，并且存在有效期，伪造请求难度加大。

1) H5通过APP中转发起网络数据请求。
2) APP计算生成设备唯一标识，用于区分唯一设备。

唯一标识生成规则：

md5(sn+安装id+imei+mac+型号+androidid)

存储规则：

l 优先存储到系统Settings中，存储更持久。

l 以隐藏文件存储到手机存储中，防止被用户删除。

APP启动即可生成唯一标识生成，存储在系统Settings或隐藏文件中，获取唯一标识读取时优先从存储中获取。唯一标识存储可以避免因为某些信息无法获取到多次计算结果不同。

3) 向Server请求加密方式及秘钥

在发起数据请求之前，先到Server申请数据加密方式及秘钥，加密方式及秘钥动态生成，秘钥设置有效期，用后即废，破解难道大，提升传输的安全性。

l IP过滤安全策略，敏感数据，限制单位时间访问频次及访问次数。

l 设备标识过滤策略，同一个标识设备，限制业务的访问次数。

l 用户过滤策略，什么用户可以访问，允许访问的次数。

l 确定数据传输的加密方式及生成秘钥。加密方式采用DES、AES、blowfish或者多重组合。

l 秘钥生成后，执行过期策略，比如10秒内有效，10秒后将秘钥删除，后续请求使用此密码判断为过期的请求。

最终Server将加密方式及秘钥返回给APP，用于下次通讯的数据加密。

4) APP根据加密方式及秘钥加密请求数据。

包括：

l 数字签名，用于校验请求数据是否完整，是否被串改

SHA256(请求url+去空格(请求体)+appId+appKey +timestamp+秘钥);

l 数据加密，根据Server返回的加密方式及秘钥对请求体加密

算法(去空格(请求体)，秘钥)

算法2(算法1(去空格(请求体)，秘钥)，秘钥)

5) APP向Server发起数据请求

l Server查询该设备秘钥是否过期，若过期则返回给APP无效请求

l 通过上述第3步生成的加密方式及秘钥对请求数据解密，然后使用请求体重新计算数字签名，同APP请求传输的数字签名进行对比，若一致说请求数据没有被串改。

Server处理完成后，将结果数据加密后返回给APP，清除加密方式及秘钥。

6) APP将Server数据解密。
7) APP将数据传输给H5页面。

5.本发明的关键点和欲保护点是什么？


1) 数据传输前先到Server申请数据加密方式及秘钥，加密后再进行传输。
2) Server秘钥存在过期机制，秘钥过期、已使用将无法通过校验。

6.用推理方式推导出本发明的优点

APP端H5使用越来越频繁，数据安全性也尤为重要，若数据不进行加密则容易被截获或串改，通过查看H5源码也容易分析出传输方式。若使用证书加密将带来证书更新维护的成本。将H5数据请求委托APP，采用动态获取加密方式及秘钥方式，加密方式及秘钥动态变化，无规律可循，方案灵活易用，安全性好，避免模拟数据攻击。
