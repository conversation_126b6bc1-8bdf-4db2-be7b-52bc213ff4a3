# AI 代码审查工程

## 项目概述

这是一个专门为AI agent提供代码审查能力的工程，通过结构化的指导原则和历史反馈知识库，帮助AI进行高质量的代码评审。

## 核心组件

### 📋 代码审查指南 (`code-review-guideline.md`)

- **重构原则**：基于《重构 改善既有代码的设计》的代码异味识别和重构技法
- **Flutter规范**：Flutter特定的Widget重构、状态管理和性能优化规则
- **Redux最佳实践**：ViewModel、State、Action、Middleware、Reducer的设计规范
- **Clean Code原则**：包含注释规范、条件判断、硬编码检查等基础代码质量要求
- **评审流程**：标准化的GitLab MR评审流程和输出格式

### 🔄 反馈处理系统 (`feedback/`)

- **反馈处理器** (`feedback_processor.py`)：自动化处理评审反馈和知识库更新
- **知识库管理**：维护正确和错误评审案例的分类存储

### 📚 知识库 (`feedback/feedback-database/`)

- **correct-reviews.md**：成功的代码评审案例和最佳实践
- **incorrect-reviews.md**：错误评审案例和纠正说明，避免重复错误

## 使用方法

### 基本评审命令

```bash
review {merge_request_url}
# 或简写
re {merge_request_url}
```

### 反馈命令

评审完成后，可通过以下命令提供反馈：

```bash
# 正确评审反馈
feedback correct "***********-a1b2c3" "评价内容"

# 错误评审反馈
feedback incorrect "***********-a1b2c3" "错误原因" "正确评审意见"

# 批量反馈
feedback batch --commands '命令1' '命令2'
feedback batch --file feedback-batch.txt
```

## 评审特色

### 🎯 精准定位

- 基于GitLab API的diff信息进行准确的行号计算
- 不依赖本地文件，仅使用MR变更内容进行评审

### 🔍 深度分析

- 识别代码异味和重构机会
- 提供具体的改进代码示例
- 确保原代码和改进建议有明确区别

### 📋 标准化输出

- 每个问题都有唯一ID：`CR-{yyyyMMdd}-{hash}`
- 结构化的Markdown格式输出
- 包含文件路径、行号、问题描述和改进建议

### 🧠 学习能力

- 每次评审前检索相关历史案例
- 避免重复历史错误
- 持续优化评审质量

## 支持的技术栈

- **Flutter**：Widget设计、状态管理、性能优化
- **Redux**：状态管理架构的最佳实践
- **Dart**：语言特性和编程规范
- **GitLab**：MR评审工作流

## 环境要求

- 环境变量：`GITLAB_ACCESS_TOKEN` 需要配置GitLab访问令牌
- Python 3.x：用于反馈处理系统
- 网络访问：需要能够访问GitLab API

## 项目架构

```
code-review/
├── README.md                      # 项目说明文档
├── code-review-guideline.md       # AI代码审查指导原则
└── feedback/                      # 反馈系统
    ├── feedback_processor.py      # 反馈处理器
    └── feedback-database/          # 知识库
        ├── correct-reviews.md      # 正确评审案例
        └── incorrect-reviews.md    # 错误评审案例
```

## 贡献指南

1. **提供反馈**：使用feedback命令对评审结果进行反馈
2. **改进规则**：基于实际使用情况优化`code-review-guideline.md`
3. **扩展知识库**：补充更多的评审案例和最佳实践

## 版本历史

- **v1.0**：初始版本，支持基本的代码评审功能
- **知识库系统**：增加历史案例学习能力
- **反馈机制**：自动化的评审质量改进系统

---

通过这个工程，AI agent能够提供专业、一致、高质量的代码评审服务，帮助开发团队提升代码质量和开发效率。
