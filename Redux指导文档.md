# Flutter - Redux使用规范

## 整体架构关系图

```mermaid
graph TD
    %% UI层
    UI[🖥️ UI 组件<br/>Flutter Widgets]
    
    %% Redux核心组件
    Store[🏪 Redux Store<br/>单一状态树]
    State[📊 State<br/>应用状态数据]
    Action[📝 Action<br/>事件描述]
    Reducer[⚙️ Reducer<br/>纯函数状态更新]
    Middleware[🔄 Middleware<br/>副作用处理]
    
    %% 连接器
    StoreConnector[🔌 StoreConnector<br/>UI-Store桥梁]
    
    %% 外部系统
    API[🌐 API 服务]
    Storage[💾 本地存储]
    Logger[📋 日志系统]
    Navigation[🧭 导航系统]
    
    %% 数据流向
    UI -.->|用户交互| Action
    Action -->|dispatch| Store
    Store -->|拦截| Middleware
    Middleware -->|副作用处理| API
    Middleware -->|副作用处理| Storage
    Middleware -->|副作用处理| Logger
    Middleware -->|副作用处理| Navigation
    Middleware -.->|dispatch新Action| Store
    Store -->|传递Action| Reducer
    Reducer -->|更新| State
    State -->|存储在| Store
    Store -->|状态变化| StoreConnector
    StoreConnector -->|重建UI| UI
    
    %% 样式
    classDef uiClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef reduxClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef middlewareClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef externalClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class UI,StoreConnector uiClass
    class Store,State,Action,Reducer reduxClass
    class Middleware middlewareClass
    class API,Storage,Logger,Navigation externalClass
```

## **State 使用规范**

> **State职责**：存储应用的所有状态数据
应该放：基础数据类型、列表、Map等可序列化的业务数据；
不应该放：函数、UI对象、异步对象、系统资源等不可序列化的值；

### 1. **不要直接修改 State**

#### ❌ **错误用法 - 直接修改State**

```dart
// lib/device/store/device_reducer.dart:294
SmartHomeState _updateDeviceTabIndex(SmartHomeState state, UpdateDeviceTabIndexAction action) {
  state.deviceState.deviceTabIndex = action.index;  // ❌ 直接修改state
  if (action.index < state.deviceState.deviceFilterMap.length) {
    state.deviceState.selectedFloor = filterModel.floorName;     // ❌ 直接修改
    state.deviceState.selectedRoom = filterModel.roomName;       // ❌ 直接修改
    state.deviceState.selectedRoomId = filterModel.roomId;       // ❌ 直接修改
  }
  return state;  // ❌ 返回被修改的原state
}
```

#### ✅ **正确用法 - 使用copyWith**

```dart
SmartHomeState _updateSceneData(SmartHomeState state, UpdateSceneData action) {
  final newSceneMap = Map<String, List<SceneItemViewModel>>.from(state.sceneState.sceneMap);
  if (serverModel != null) {
    newSceneMap[familyId] = SceneReducerUtil.getSceneList(serverModel);
  }
  
  return state.copyWith(
    sceneState: state.sceneState.copyWith(
      sceneMap: newSceneMap,
      visibility: curSceneList.isNotEmpty,
    )
  );
}
```

### 2. **每个应用程序只有一个 Redux Store**

#### ✅ **正确用法 - 单一Store**

```dart
// lib/store/smart_home_store.dart
final Store<SmartHomeState> smartHomeStore = Store<SmartHomeState>(
    smartHomeReducer,
    initialState: SmartHomeState(),
    middleware: smartHomeMiddleware);

// 全应用使用同一个store
// lib/smart_home.dart:537
StoreConnector<SmartHomeState, bool>(
  converter: (Store<SmartHomeState> store) => store.state.isEditState,
  // ...
)
```

### 3. **不要在 State 或 Action 中放入不可序列化的值**

// ❌ 不可序列化的值包括：  

- Functions (VoidCallback, Function, etc.)
- UI Objects (BuildContext, Widget, GlobalKey, etc.)
- Controllers (AnimationController, TextEditingController, etc.)
- Async Objects (Future, Stream, StreamSubscription, etc.)
- System Resources (Timer, File, Socket, etc.)
- Complex Class Instances (Service classes, Manager classes, etc.)
- Circular References
- Symbols, Types, RegExp

#### ❌ **错误用法 - 存储BuildContext**

```dart
// lib/device/store/device_action.dart:181
class UpdatePopupContextAction extends DeviceBaseAction {
  UpdatePopupContextAction(this.context);
  final BuildContext? context;  // ❌ 存储UI对象BuildContext
}
```

#### ✅ **正确用法 - 序列化数据**

```dart
// Action只包含数据
class SceneExecuteAction extends SceneBaseAction {
  final String sceneId;
  final String sceneName;
  final String familyId;
  
  SceneExecuteAction({
    required this.sceneId,
    required this.sceneName,
    required this.familyId,
  });
}

// UI层处理导航
StoreListener<SmartHomeState>(
  onChanged: (context, state) {
    if (state.sceneState.shouldShowExecutionUI) {
      ManualOperation.execute({...}, true, familyId, context, 'ismodel');
    }
  },
  child: SceneWidget(),
)
```

### 4. **使用 Freezed确保State不可变更新**

#### ❌ **错误用法 - 手写样板代码**

```dart
// lib/store/smart_home_state.dart:72
SmartHomeState copyWith({
  bool? isEditState,
  bool? isScrollableScrollPhysics,
  bool? isLogin,
  FamilyState? familyState,
  WholeHouseState? wholeHouseState,
  // ... 13个参数，大量重复代码
}) {
  return SmartHomeState(
      isEditState: isEditState ?? this.isEditState,
      isScrollableScrollPhysics: isScrollableScrollPhysics ?? this.isScrollableScrollPhysics,
      isLogin: isLogin ?? this.isLogin,
      familyState: familyState ?? this.familyState,
      // ... 还有很多重复代码
  );
}
```

#### ✅ **正确用法 - Freezed自动生成**

```dart
@freezed
class SmartHomeState with _$SmartHomeState {
  const factory SmartHomeState({
    @Default(false) bool isEditState,
    @Default(false) bool isScrollableScrollPhysics,
    @Default(false) bool isLogin,
    @Default(FamilyState()) FamilyState familyState,
    @Default(WholeHouseState()) WholeHouseState wholeHouseState,
    @Default(SceneState()) SceneState sceneState,
    @Default(DeviceState()) DeviceState deviceState,
    // ... Freezed自动生成所有方法
  }) = _SmartHomeState;

  factory SmartHomeState.fromJson(Map<String, dynamic> json) => 
      _$SmartHomeStateFromJson(json);
}

// 从80行代码减少到15行代码！
```

---

## **Action 设计规范**

> **Action职责**：描述应用中"发生了什么事件"
应该放：事件名称、相关的业务数据、事件上下文信息；
不应该放：业务逻辑、UI操作、异步处理、函数回调；

### 1. **Action命名：事件风格 + 语义化**

#### ❌ **错误用法 - Setter风格和命名不一致**

```dart
// lib/device/store/device_action.dart
class UpdateDeviceTabIndexAction extends DeviceBaseAction {
  UpdateDeviceTabIndexAction(this.index);
  final int index;  // ❌ 简单的setter风格
}

class UpdateDeviceStatusAction extends DeviceBaseAction {
  UpdateDeviceStatusAction(this.deviceStatus);
  final DeviceStatus deviceStatus;  // ❌ 直接设置状态
}

class SmallCardDragFinishedAction extends DeviceBaseAction {}  // ❌ 不一致的模式
class UpdateImageRefreshCountAction {}                        // ❌ 没有继承BaseAction
class FetchCameraMsgAction extends DeviceBaseAction {}        // ❌ 缺少Data后缀
```

#### ✅ **正确用法 - 事件风格 + 统一命名**

```dart
// 描述"发生了什么"而不是"设置什么"，使用统一的 [动词][名词]Action 模式
class DeviceTabChangedAction extends DeviceBaseAction {
  final int previousIndex;
  final int newIndex;
  final String userId;
  final DateTime changedAt;
  
  DeviceTabChangedAction({
    required this.previousIndex,
    required this.newIndex,
    required this.userId,
    required this.changedAt,
  });
}

class DeviceStatusChangedAction extends DeviceBaseAction {
  final String deviceId;
  final DeviceStatus previousStatus;
  final DeviceStatus newStatus;
  final String changeReason;
  
  DeviceStatusChangedAction({
    required this.deviceId,
    required this.previousStatus,
    required this.newStatus,
    required this.changeReason,
  });
}

// 统一使用 [动词][名词]Action 模式
class CompleteSmallCardDragAction extends DeviceBaseAction {}
class UpdateImageRefreshCountAction extends DeviceBaseAction {}
class FetchCameraMsgDataAction extends DeviceBaseAction {}

// 或者使用事件风格的命名
class SmallCardDragCompletedAction extends DeviceBaseAction {}
class CameraMsgDataLoadedAction extends DeviceBaseAction {}
class ImageRefreshCountIncreasedAction extends DeviceBaseAction {}
```

### 2. **统一使用命名参数**

#### ❌ **错误用法 - 参数风格混合**

```dart
// lib/device/store/device_action.dart:23
class UpdateDeviceInfoMapAction extends DeviceBaseAction {
  UpdateDeviceInfoMapAction(this.originalDeviceInfoMap, this.familyId,  // ❌ 位置参数
      {required this.traceId, required this.traceType});                // ❌ 混合命名参数
  
  int traceId;
  TraceType traceType;
  String familyId;
  Map<String, DeviceInfoModel> originalDeviceInfoMap;
}

// lib/scene/store/scene_action.dart:28
class SceneExecuteAction extends SceneBaseAction {
  SceneExecuteAction(this.item, this.context);  // ❌ 纯位置参数，容易搞错顺序
  SceneItemViewModel item;
  BuildContext context;
}
```

#### ✅ **正确用法 - 命名参数**

```dart
class UpdateDeviceInfoMapAction extends DeviceBaseAction {
  final Map<String, DeviceInfoModel> originalDeviceInfoMap;
  final String familyId;
  final int traceId;
  final TraceType traceType;

  UpdateDeviceInfoMapAction({
    required this.originalDeviceInfoMap,
    required this.familyId,
    required this.traceId,
    required this.traceType,
  });
}

class SceneExecuteAction extends SceneBaseAction {
  final String sceneId;
  final String sceneName;
  final String familyId;
  
  SceneExecuteAction({
    required this.sceneId,
    required this.sceneName,
    required this.familyId,
  });
}
```

---

## **Reducer 编写规范**

> **Reducer职责**：根据Action更新State，必须是纯函数
应该放：状态计算逻辑、数据转换逻辑；
不应该放：API调用、UI操作、异步处理、副作用操作、日志输出；

### 1. **Reducer 不能有副作用**

#### ❌ **错误用法 - Reducer包含副作用**

```dart
// lib/device/store/device_reducer.dart:225 - 在Reducer中调用UI操作
SmartHomeState _updateDeviceFilter(SmartHomeState state, UpdateDeviceFilterAction action) {
  // ... 状态更新逻辑
  if (tabIndex >= 0) {
    SmartHomePresenter.changeTab(tabIndex);  // ❌ 副作用：UI操作
    state.deviceState.deviceTabIndex = tabIndex;
    // ...
  }
  return state;
}
```

#### ✅ **正确用法 - 纯函数Reducer**

```dart
// 1. Reducer只负责状态更新，不包含副作用
SmartHomeState _updateDeviceFilter(SmartHomeState state, UpdateDeviceFilterAction action) {
  // ... 状态更新逻辑
  if (tabIndex >= 0) {
    // ✅ 只更新状态，不调用UI操作
    return state.copyWith(
      deviceState: state.deviceState.copyWith(
        deviceTabIndex: tabIndex,
        selectedFloor: filterModelList[tabIndex].floorName,
        selectedRoom: filterModelList[tabIndex].roomName,
      )
    );
  }
  return state;
}

// 2. 在Middleware中处理副作用
class DeviceMiddleware implements MiddlewareClass<SmartHomeState> {
  int _previousTabIndex = -1;
  
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    // 先让Reducer更新状态
    next(action);
    
    // 然后处理副作用
    if (action is UpdateDeviceFilterAction) {
      final int newTabIndex = store.state.deviceState.deviceTabIndex ?? -1;
      
      if (newTabIndex != _previousTabIndex && newTabIndex >= 0) {
        // ✅ 在Middleware中处理UI操作
        _handleTabChange(newTabIndex);
        _previousTabIndex = newTabIndex;
      }
    }
  }

  void _handleTabChange(int index) {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (smartHomePageController != null && smartHomePageController!.hasClients) {
        smartHomePageController!.jumpToPage(index);
      }
    });
    
    // 埋点统计
    if (index == 0) {
      gioTrack(SmartHomeConstant.quickListExposureGio);
    }
  }
}
```

## **Middleware 使用规范**

> **Middleware职责**：处理所有副作用和复杂逻辑
应该放：API调用、异步操作、UI操作、日志记录、缓存处理、业务流程控制；
不应该放：状态计算逻辑（应在Reducer中）；

### 1. **所有副作用必须在Middleware中处理**

#### ✅ **正确用法 - Middleware处理副作用**

```dart
// lib/scene/store/scene_middleware.dart:18
class SceneMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    if (action is SceneExecuteAction) {
      // 副作用：网络连接检查
      final ConnectivityResult connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);  // 副作用：UI提示
        return;
      }
      
      // 副作用：存储操作
      await Storage.setTemporaryStorage(CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET, '1');
      
      // 副作用：场景执行
      if (action.context.mounted) {
        ManualOperation.execute(/* ... */);  // 副作用：外部操作
      }
      return;
    }
    next(action);
  }
}
```

### 2. **Middleware与Presenter的职责分离**

```dart
优先级低，后续考虑去掉Presenter
Middleware负责处理异步调用，包括数据请求和存储接口调用后，dispatch发送更新数据等逻辑；
接口请求数据进一步封装到Service中；
```

---

## **StoreConnector 使用规范**

> **StoreConnector职责**：连接Redux Store和UI组件
应该放：状态选择逻辑、UI数据转换；
不应该放：复杂的业务逻辑、直接的状态修改、副作用操作；

### 1. **使用 distinct: true 避免 UI 重绘**

#### ✅ **正确用法 - 使用distinct**

```dart
// lib/smart_home.dart:537
StoreConnector<SmartHomeState, bool>(
  distinct: true,  // ✅ 正确使用distinct避免不必要重建
  converter: (Store<SmartHomeState> store) {
    return store.state.isEditState;
  },
  builder: (BuildContext context, bool showEditWidget) {
    // ... UI构建逻辑
  },
)
```

### 2. **避免在组件中直接访问全局Store**

#### ❌ **错误用法 - 直接访问Store**

```dart
// 避免这样直接访问全局store
class BadDeviceWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        // ❌ 直接访问全局store
        smartHomeStore.dispatch(UpdateDeviceTabIndexAction(1));
      },
      child: Text('切换设备'),
    );
  }
}
```

#### ✅ **正确用法 - StoreProvider**

```dart
// ✅ 正确的简化版本
StoreConnector<SmartHomeState, SomeViewModel>(
  converter: (store) => SomeViewModel(
    data: store.state.someData,
    isLoading: store.state.isLoading,
  ),
  builder: (context, viewModel) {    
    return SomeWidget(
      viewModel: viewModel,
      onAction: () => StoreProvider.of<SmartHomeState>(context)  // ✅ 注意箭头函数
          .dispatch(SomeAction()),  // ✅ 注意没有分号
    );
  },
)}
```

---

## **重构优先级**

### **P0 - 8月份修改上线**

1. **修复State直接修改**：在`device_reducer.dart`和`scene_reducer.dart`中
2. **迁移到Freezed**：逐步替换手写State
3. **移除Action中的BuildContext**：`UpdatePopupContextAction`和`SceneExecuteAction`

### **P1 - 9月份修改上线**

1. **重构Action命名**：统一为事件风格
2. **统一Action构造函数**：全部改为命名参数
3. **优化StoreConnector使用**

### **P2 - 10月份修改上线**

1. **清理Middleware与Presenter的职责**

---
